\chapter{Analysis and Specification of Requirements}

\section{Introduction}
This chapter focuses on defining system requirements, actors, use cases, architecture, and specifications needed for building the MED4SOLUTIONS platform. We present a comprehensive analysis of the digital health solution requirements, establishing the foundation for medication management and delivery system development.

\section{Needs Analysis}

\subsection{Actors}
The system involves four primary actors, each with specific roles and responsibilities:

\begin{description}
    \item[Patient] Orders med\-i\-ca\-tions, uploads prescriptions, tracks orders, and manages their profile.
    \item[Pharmacy] Validates prescriptions, manages inventory, and processes orders.
    \item[Delivery Personnel] Updates order status and ensures delivery to the patient.
    \item[System Administrator] Oversees system security, performance, and user management.
\end{description}

\subsection{Functional Requirements}
The key system features are organized logically into the following categories:

\begin{description}
    \item[Authentication and Registration Management] Multi-factor authentication for secure access, account activation via email verification, and role-based access control for different user types.
    \item[Patient Profile Management] Personal details update (name, address, phone number), prescription management and history, payment methods management, and delivery preferences configuration.
    \item[Prescription Management] Upload prescriptions as photos or PDF files, AI-based scanning and digitization of prescriptions, pharmacy validation and verification processes, and prescription renewal alerts and no\-ti\-fi\-ca\-tions.
    \item[Ordering and Stock Management] Browse and select med\-i\-ca\-tions from available inventory, request validation from pharmacies, real-time pharmacy stock updates, and alternative med\-i\-ca\-tion suggestions when items are out of stock.
    \item[Order Tracking and Delivery Management] Real-time order tracking throughout the delivery process, automated no\-ti\-fi\-ca\-tions for order status updates, delivery status updates by delivery personnel, delivery confirmation by patients, and rescheduling options for missed deliveries.
    \item[Messaging and Communication] Real-time chat between patients and pharmacies, com\-mu\-ni\-ca\-tion channels between patients and delivery personnel, and automated no\-ti\-fi\-ca\-tions and alerts.
    \item[Notification and Reminder Management] Alerts for prescription renewals, personalized med\-i\-ca\-tion reminders, order status no\-ti\-fi\-ca\-tions, and system maintenance and update no\-ti\-fi\-ca\-tions.
    \item[Integration Management] Pharmacy system integration for inventory management, delivery service integration for logistics, payment gateway integration for secure transactions, and healthcare provider integration for prescription management.
    \item[Administration Management] Role-based user access control, system performance monitoring, secure data management and backup, and user account management and support.
\end{description}

\subsection{Non-Functional Requirements}
The system must meet the following performance, security, scalability, and reliability requirements:

\begin{itemize}
    \item Response time < 2 seconds
    \item GDPR compliance for data protection
    \item Support for 1000+ concurrent users
    \item 99.9\% uptime guarantee
    \item Secure data transmission (HTTPS)
    \item End-to-end encryption for sensitive data
    \item Multi-factor authentication (MFA) implementation
    \item Daily automated backups and disaster recovery procedures
    \item Horizontal scaling capabilities and load balancing
    \item Accessibility compliance (WCAG guidelines)
\end{itemize}

\section{Requirements Specification}

\subsection{General Use Case Diagram}
Figure \ref{fig:usecase} illustrates the interactions between different actors and the system functionalities, showing the comprehensive scope of the MED4SOLUTIONS platform.

\begin{figure}[H]
\centering
\includegraphics[height=0.85\textheight]{img/Use case diagram.png}
\caption{General Use Case Diagram}
\label{fig:usecase}
\end{figure}

\subsection{Analysis Class Diagram}
Figure \ref{fig:classdiagram} presents the analysis class diagram that defines the core entities and their relationships within the system architecture.

\begin{landscape}
\begin{figure}[p]
\centering
\includegraphics[width=\linewidth,height=\textheight,keepaspectratio]{img/ClassDiagramm_PFE_FinalVersion (4).png}
\caption{Analysis Class Diagram}
\label{fig:classdiagram}
\end{figure}
\end{landscape}



\newpage
\subsection{Product Backlog}
The product backlog provides a prioritized list of features and user stories for the development process. Table \ref{table:product_backlog} presents each item with its ID, feature description, user story, priority level, and estimation points.

\begin{longtable}{|c|p{3.2cm}|c|p{4.5cm}|c|c|}
\hline
\textbf{ID} & \textbf{Feature} & \textbf{ID User Story} & \textbf{User Story} & \textbf{Priority} & \textbf{Estimation} \\
\hline
\endfirsthead

\hline
\textbf{ID} & \textbf{Feature} & \textbf{ID User Story} & \textbf{User Story} & \textbf{Priority} & \textbf{Estimation} \\
\hline
\endhead

\hline
\endfoot

\hline
\caption{Product Backlog}
\label{table:product_backlog}
\endlastfoot

\multirow{5}{*}{1} & \multirow{5}{*}{\parbox{3.2cm}{\raggedright Registration and Authentication Management}} & 1.1 & As a patient, I would like to authenticate securely with MFA & 80 & Medium \\
\cline{3-6}
 &  & 1.2 & As a pharmacist, I want to create a patient account and activate it, so that the patient can access the application. & 135 & High \\
\cline{3-6}
 &  & 1.3 & As a patient, I want to activate my account by setting a password, so that I can access the application. & 180 & High \\
\cline{3-6}
 &  & 1.4 & As a pharmacist, I want to send an activation link and a temporary password to the patient, so that they can activate their account. & 120 & High \\
 \cline{3-6}
 &  & 1.5 & As a Patient, I want to restore my password with email verification code in case it's forgotten. & 175 & High \\
\hline
\multirow{3}{*}{2} & \multirow{3}{*}{\parbox{3.2cm}{\raggedright Patient Profile Management}} & 2.1 & As a patient, I want to update my personal details & 165 & High \\
\cline{3-6}
 &  & 2.2 & As a patient, I want to see a clear summary of my personal information and preferences in a single interface, so I can easily review and update them. & 185 & High \\
\cline{3-6}
 &  & 2.3 & As a pharmacist, I want to view patient profile information (name, phone number, address, delivery preferences, prescriptions) so that I can provide personalized and tailored service. & 150 & High \\
\hline
\multirow{4}{*}{3} & \multirow{4}{*}{\parbox{3.2cm}{\raggedright Prescription Management}} & 3.1 & As a patient, I want to upload my prescription as a photos or PDF files or take a instant photos & 200 & High \\
\cline{3-6}
 &  & 3.2 & As a patient, I want the system to process my prescription using AI & 195 & High \\
\cline{3-6}
 &  & 3.3 & As a patient, I want to receive a no\-ti\-fi\-ca\-tion when the pharmacy requires my original prescription so that I can arrange for its pickup & 155 & High \\
\cline{3-6}
 &  & 3.4 & As a system, I want to use OCR to scan and translate prescriptions into a structured format while retaining the original files. & 140 & High \\
\hline
\multirow{8}{*}{4} & \multirow{8}{*}{\parbox{3.2cm}{\raggedright Order Management}} & 4.1 & As a patient, I want to track my order in real time & 190 & High \\
\cline{3-6}
 &  & 4.2 & As a patient, I want to receive no\-ti\-fi\-ca\-tions for order status updates & 170 & High \\
\cline{3-6}
 &  & 4.3 & As a patient, I want to confirm delivery upon receipt & 130 & High \\
\cline{3-6}
 &  & 4.4 & As a patient, I want to be able to view the items available for order in my interface & 125 & High \\
\cline{3-6}
 &  & 4.5 & As a patient, I want to add items to my cart and submit my order for processing. & 160 & High \\
\cline{3-6}
 &  & 4.6 & As a system, I want to automatically create a patient package and notify the pharmacy once the order is validated. & 115 & High \\
\cline{3-6}
 &  & 4.7 & As a patient, I would like to add a delivery preference so that I can receive my medication at a specific time. & 145 & High \\
\cline{3-6}
 &  & 4.8 & As a pharmacy, I want to confirm orders before processing & 70 & Medium \\
\hline
\multirow{3}{*}{5} & \multirow{3}{*}{\parbox{3.2cm}{\raggedright Delivery Management}} & 5.1 & As a delivery personnel, I want to update the order status (Picked up, Delivered) & 85 & Medium \\
\cline{3-6}
 &  & 5.2 & As a delivery personnel, I want to mark an order as "Delivery Attempted" if the patient is unavailable so that I can retry the delivery later. & 90 & Medium \\
\cline{3-6}
 &  & 5.3 & As a patient, I want to view my order history to see their details and status. & 100 & Low \\
\hline
\multirow{3}{*}{6} & \multirow{3}{*}{\parbox{3.2cm}{\raggedright No\-ti\-fi\-ca\-tions Management}} & 6.1 & As a patient, I want to receive alerts for prescription renewals & 105 & Medium \\
\cline{3-6}
 &  & 6.2 & As a patient, I want to read and unread no\-ti\-fi\-ca\-tions & 110 & Medium \\
\cline{3-6}
 &  & 6.3 & As a pharmacist, I want to receive no\-ti\-fi\-ca\-tion when a patient add a prescription with a package type content & 50 & Medium \\
\hline
\multirow{2}{*}{7} & \multirow{2}{*}{\parbox{3.2cm}{\raggedright Stock Management}} & 7.1 & As a pharmacy/Admin, I want to create/update/delete products in my stock & 95 & Medium \\
\cline{3-6}
 &  & 7.2 & As a pharmacy/Admin I want to create/update/delete product categories & 60 & Medium \\
\hline
\end{longtable}

\subsection{Sprint Planning}
Sprint planning is a crucial component of the Scrum methodology that involves breaking down the product backlog into manageable development cycles called sprints. Each sprint represents a time-boxed iteration where specific features are developed, tested, and delivered. This approach enables iterative development, continuous feedback, and adaptive planning throughout the project lifecycle. Sprint planning ensures that development teams can focus on delivering high-priority features while maintaining flexibility to respond to changing requirements and stakeholder feedback.

The MED4SOLUTIONS project is organized into five strategic sprints, each targeting specific feature sets and platform components. Table \ref{table:sprint_planning} outlines the sprint structure, covering both mobile and web platform development with dedicated time for AI model training and system integration.

\begin{longtable}{|c|p{8cm}|c|}
\hline
\textbf{Sprint} & \textbf{Feature(s) Covered} & \textbf{Duration (weeks)} \\
\hline
\endfirsthead

\hline
\textbf{Sprint} & \textbf{Feature(s) Covered} & \textbf{Duration (weeks)} \\
\hline
\endhead

\hline
\endfoot

\hline
\caption{Sprint Planning}
\label{table:sprint_planning}
\endlastfoot

1 & \textbf{Authentication + Profile Management (Mobile)} & 4 \\
\hline
2 & \textbf{Prescription Management (Mobile + Web)} \newline Upload, no\-ti\-fi\-ca\-tion, OCR, viewing & 3 \\
\hline
3 & \textbf{Order Management + Stock Management (Mobile + Web)} & 5 \\
\hline
4 & \textbf{AI Prescription Processing (Model training + integration)} & 6 \\
\hline
5 & \textbf{No\-ti\-fi\-ca\-tions Management (Mobile + Web) + Buffer/Polish/QA} \newline Final adjustments, polish & 2 \\
\hline
\end{longtable}

\section{Overall Application Architecture}

\subsection{Physical Architecture}
Figure \ref{fig:physical_architecture} illustrates the physical deployment architecture of the MED4SOLUTIONS platform, showing the distribution of components across different servers and environments.

\begin{figure}[H]
\centering
\includegraphics[width=0.8\textwidth]{img/Physical_architechture_PFE.png}
\caption{Physical Architecture}
\label{fig:physical_architecture}
\end{figure}

\subsection{Logical Architecture}
Figure \ref{fig:logical_architecture} presents the logical architecture of the system, demonstrating the relationships and interactions between different software components and layers.

\begin{figure}[H]
\centering
\includegraphics[width=0.8\textwidth]{img/Logical_architechture_PFE.png}
\caption{Logical Architecture}
\label{fig:logical_architecture}
\end{figure}

\subsection{Technology Stack}
The MED4SOLUTIONS platform is built using a modern technology stack that ensures scalability, performance, and maintainability:

\begin{description}
    \item[Backend] Spring Boot framework providing REST API services, authentication mechanisms, and scheduling capabilities for medication reminders and order processing.
    \item[Frontend] Angular framework delivering a responsive user interface that adapts to various devices and screen sizes, ensuring optimal user experience across platforms.
    \item[AI Layer] Python-based artificial intelligence components handling OCR (Optical Character Recognition) for prescription scanning, prescription validation algorithms, and intelligent chatbot functionality for user assistance.
    % \item[Real-time Communication] WebSocket implementation enabling real-time chat functionality between users and pharmacies, as well as live delivery status updates throughout the order fulfillment process.
    \item[Database] MongoDB NoSQL database providing flexible data storage for user profiles, prescriptions, orders, and system logs with high performance and scalability.
    \item[Deployment] Dockerized containerization setup deployed on Linux servers, ensuring consistent environments across development, testing, and production stages with easy scaling and maintenance capabilities.
\end{description}

\section{Conclusion}
This chapter has defined the core user needs and technical foundations that guide the system design and development of the MED4SOLUTIONS platform. Through comprehensive requirements analysis, actor identification, use case specification, and architectural planning, we have established a solid foundation for building a robust digital health solution that meets the real-world needs of patients, pharmacies, and delivery personnel in the healthcare ecosystem.
