@startuml stock_sequence
!theme plain
skinparam sequenceMessageAlign center
skinparam backgroundColor #FAFAFA
skinparam participant {
    BackgroundColor #E8F4FD
    BorderColor #1E88E5
    FontSize 12
}
skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #FF9800
    FontSize 12
}
skinparam note {
    BackgroundColor #F3E5F5
    BorderColor #9C27B0
}

title Stock Management Sequence Diagram

actor "Pharmacist" as Ph
participant "Web App" as Web
participant "Product\nController" as Controller
participant "Product\nCreate Use Case" as CreateUC
participant "Product\nUpdate Use Case" as UpdateUC
participant "Product\nRepository" as ProductRepo
participant "Category\nRepository" as CategoryRepo
participant "MongoDB\nDatabase" as DB

== Create New Product ==

Ph -> Web: Access product management
activate Web
Web -> Web: Display create product form
Web --> Ph: Show product form

Ph -> Web: Fill product details and submit
Web -> Controller: POST /products/create\n{product data}
activate Controller

Controller -> Controller: Validate user role\n(pharmacist/admin)

Controller -> CreateUC: execute(command, pharmacyId)
activate CreateUC

CreateUC -> CategoryRepo: findById(categoryId)
activate CategoryRepo
CategoryRepo -> DB: findOne({id: categoryId})
activate DB
DB --> CategoryRepo: category details
deactivate DB
CategoryRepo --> CreateUC: category
deactivate CategoryRepo

note over CreateUC: Prepare product data:\n• Generate UUID\n• Set pharmacy ID\n• Calculate stock status

CreateUC -> ProductRepo: create(productData)
activate ProductRepo
ProductRepo -> DB: create({id, pharmacyId, name,\nquantity, price, ...})
activate DB
DB --> ProductRepo: created product
deactivate DB
ProductRepo --> CreateUC: product
deactivate ProductRepo

CreateUC --> Controller: created product
deactivate CreateUC
Controller --> Web: {success: true, product}
deactivate Controller
Web --> Ph: Product created successfully
deactivate Web

== Update Product and Stock Status ==

Ph -> Web: Edit existing product
activate Web
Web -> Controller: GET /products/{id}
activate Controller

Controller -> ProductRepo: findById(productId)
activate ProductRepo
ProductRepo -> DB: findOne({id: productId})
activate DB
DB --> ProductRepo: product details
deactivate DB
ProductRepo --> Controller: product
deactivate ProductRepo

Controller --> Web: {success: true, product}
deactivate Controller
Web --> Ph: Display product edit form
deactivate Web

Ph -> Web: Update product details\n(quantity, price, etc.)
activate Web
Web -> Controller: PUT /products/update\n{updated data}
activate Controller

Controller -> UpdateUC: execute(command)
activate UpdateUC

UpdateUC -> ProductRepo: findById(productId)
activate ProductRepo
ProductRepo -> DB: findOne({id: productId})
activate DB
DB --> ProductRepo: existing product
deactivate DB
ProductRepo --> UpdateUC: product
deactivate ProductRepo

note over UpdateUC: Prepare update data:\n• Auto-update stock status\n• Validate changes

UpdateUC -> ProductRepo: update(productId, updateData)
activate ProductRepo
ProductRepo -> DB: updateOne({id: productId},\n{quantity, stockStatus, ...})
activate DB
DB --> ProductRepo: updated product
deactivate DB
ProductRepo --> UpdateUC: product
deactivate ProductRepo

UpdateUC --> Controller: updated product
deactivate UpdateUC
Controller --> Web: {success: true, product}
deactivate Controller
Web --> Ph: Product updated successfully
deactivate Web

== Search and Filter Products ==

Ph -> Web: Access inventory dashboard
activate Web
Web -> Controller: GET /products/pharmacy?\nsearch=&stockStatus=&actif=
activate Controller

Controller -> Controller: Extract pharmacy ID\nfrom user

Controller -> ProductRepo: findForPharmacyWithFilters\n(pharmacyId, filters)
activate ProductRepo
ProductRepo -> DB: find({pharmacyId, ...filters})\n.sort({createdAt: -1})
activate DB
DB --> ProductRepo: filtered products
deactivate DB
ProductRepo --> Controller: products list
deactivate ProductRepo

Controller --> Web: {success: true,\nproducts, total}
deactivate Controller
Web --> Ph: Display filtered inventory
deactivate Web

== Category Management ==

Ph -> Web: Manage product categories
activate Web
Web -> Controller: GET /categories/getAll
activate Controller

Controller -> CategoryRepo: findAll()
activate CategoryRepo
CategoryRepo -> DB: find({}).sort({name: 1})
activate DB
DB --> CategoryRepo: all categories
deactivate DB
CategoryRepo --> Controller: categories
deactivate CategoryRepo

Controller --> Web: {success: true, categories}
deactivate Controller
Web --> Ph: Display categories list
deactivate Web

Ph -> Web: Create new category
activate Web
Web -> Controller: POST /categories/create\n{name}
activate Controller

Controller -> CategoryRepo: create({id: UUID, name})
activate CategoryRepo
CategoryRepo -> DB: create({id, name})
activate DB
DB --> CategoryRepo: created category
deactivate DB
CategoryRepo --> Controller: category
deactivate CategoryRepo

Controller --> Web: {success: true, category}
deactivate Controller
Web --> Ph: Category created
deactivate Web

== Stock Status Automation ==

note over UpdateUC, ProductRepo: Automatic stock status update logic

UpdateUC -> UpdateUC: Check quantity value

alt quantity > 0
    UpdateUC -> UpdateUC: Set stockStatus = IN_STOCK
else quantity = 0
    UpdateUC -> UpdateUC: Set stockStatus = OUT_OF_STOCK
end

UpdateUC -> ProductRepo: update with new stockStatus
activate ProductRepo
ProductRepo -> DB: updateOne({stockStatus})
activate DB
DB --> ProductRepo: updated product
deactivate DB
deactivate ProductRepo

@enduml
