`@startuml order_sequence
!theme plain
skinparam sequenceMessageAlign center
skinparam backgroundColor #FAFAFA
skinparam participant {
    BackgroundColor #E8F4FD
    BorderColor #1E88E5
    FontSize 12
}
skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #FF9800
    FontSize 12
}
skinparam note {
    BackgroundColor #F3E5F5
    BorderColor #9C27B0
}

title Order Processing Sequence Diagram

actor "Patient" as Patient
actor "Pharmacist" as Pharmacist
actor "Delivery\nPersonnel" as DeliveryPerson
participant "Mobile App" as MobileApp
participant "Web App" as WebApp
participant "Order\nController" as Controller
participant "Order Add\nUse Case" as AddUC
participant "Order Confirm\nUse Case" as ConfirmUC
participant "Pharmacist Confirm\nUse Case" as PharmacistUC
participant "Mark Delivered\nUse Case" as DeliveredUC
participant "Order\nRepository" as OrderRepo
participant "Product\nRepository" as ProductRepo
participant "MongoDB\nDatabase" as DB
participant "Notification\nService" as NotificationSvc

== Add Product to Basket ==

Patient -> MobileApp: Browse products and select item
activate MobileApp
MobileApp -> Controller: POST /basket/add {productId}
activate Controller

Controller -> AddUC: execute(command)
activate AddUC

AddUC -> OrderRepo: findBasketByPatientId(patientId)
activate OrderRepo
OrderRepo -> DB: findOne({patientId, flag: false})
activate DB
DB --> OrderRepo: existing basket or null
deactivate DB
deactivate OrderRepo

AddUC -> ProductRepo: findById(productId)
activate ProductRepo
ProductRepo -> DB: findOne({id: productId})
activate DB
DB --> ProductRepo: product details
deactivate DB
ProductRepo --> AddUC: product with price
deactivate ProductRepo

alt No existing basket
    AddUC -> OrderRepo: create(new basket)
    activate OrderRepo
    OrderRepo -> DB: create({patientId, productIds: [productId], ...})
    activate DB
    DB --> OrderRepo: created basket
    deactivate DB
    deactivate OrderRepo
else Existing basket
    AddUC -> OrderRepo: update(basketId, {add productId})
    activate OrderRepo
    OrderRepo -> DB: updateOne({id: basketId},\n{$push: {productIds: productId}})
    activate DB
    DB --> OrderRepo: updated basket
    deactivate DB
    deactivate OrderRepo
end

AddUC --> Controller: basket result
deactivate AddUC
Controller --> MobileApp: {success: true, basket}
deactivate Controller
MobileApp --> Patient: Product added to basket
deactivate MobileApp

== Confirm Order ==

Patient -> MobileApp: Review basket and confirm order
activate MobileApp
MobileApp -> Controller: POST /basket/confirm\n{quantity, deliveryManNote}
activate Controller

Controller -> ConfirmUC: execute(command)
activate ConfirmUC

ConfirmUC -> OrderRepo: findBasketByPatientId(patientId)
activate OrderRepo
OrderRepo -> DB: findOne({patientId, flag: false})
activate DB
DB --> OrderRepo: basket
deactivate DB
deactivate OrderRepo

ConfirmUC -> ProductRepo: findById(productId)
activate ProductRepo
ProductRepo -> DB: findOne({id: productId})
activate DB
DB --> ProductRepo: product details
deactivate DB
ProductRepo --> ConfirmUC: product with pricing
deactivate ProductRepo

note over ConfirmUC: Calculate total amount:\n(price - discount) × quantity

ConfirmUC -> OrderRepo: update(basketId, {flag: true,\nstatus: PENDING, ...})
activate OrderRepo
OrderRepo -> DB: updateOne({id: basketId},\n{flag: true, status: "PENDING", ...})
activate DB
DB --> OrderRepo: pending order
deactivate DB
OrderRepo --> ConfirmUC: order
deactivate OrderRepo

ConfirmUC --> Controller: pending order
deactivate ConfirmUC

Controller -> NotificationSvc: sendOrderNotificationToPharmacy(order)
activate NotificationSvc
NotificationSvc --> Controller: notification sent
deactivate NotificationSvc

Controller --> MobileApp: {success: true, order}
deactivate Controller
MobileApp --> Patient: Order submitted for pharmacy approval
deactivate MobileApp

== Pharmacist Views and Confirms Order ==

Pharmacist -> WebApp: Access order management dashboard
activate WebApp
WebApp -> Controller: GET /orders/filter?\ndate=today&status=PENDING
activate Controller

Controller -> OrderRepo: findByFilters({status: PENDING,\norderDate: today})
activate OrderRepo
OrderRepo -> DB: find({status: "PENDING",\norderDate: {...}})
activate DB
DB --> OrderRepo: pending orders
deactivate DB
OrderRepo --> Controller: orders list
deactivate OrderRepo

Controller --> WebApp: {success: true, orders}
deactivate Controller
WebApp --> Pharmacist: Display pending orders
deactivate WebApp

Pharmacist -> WebApp: Select order and review details
activate WebApp
WebApp -> Controller: GET /orders/{orderId}
activate Controller

Controller -> OrderRepo: findById(orderId)
activate OrderRepo
OrderRepo -> DB: findOne({id: orderId})
activate DB
DB --> OrderRepo: order details
deactivate DB
OrderRepo --> Controller: order
deactivate OrderRepo

Controller --> WebApp: {success: true, order}
deactivate Controller
WebApp --> Pharmacist: Display order details
deactivate WebApp

Pharmacist -> WebApp: Confirm order with quantity\nand delivery note
activate WebApp
WebApp -> Controller: POST /orders/pharmacistConfirm\n{patientId, quantity, deliveryManNote}
activate Controller

Controller -> PharmacistUC: execute(command)
activate PharmacistUC

PharmacistUC -> OrderRepo: findBasketByPatientId(patientId)
activate OrderRepo
OrderRepo -> DB: findOne({patientId, flag: true,\nstatus: "PENDING"})
activate DB
DB --> OrderRepo: pending order
deactivate DB
deactivate OrderRepo

PharmacistUC -> ProductRepo: findById(productId)
activate ProductRepo
ProductRepo -> DB: findOne({id: productId})
activate DB
DB --> ProductRepo: product details
deactivate DB
ProductRepo --> PharmacistUC: product with pricing
deactivate ProductRepo

note over PharmacistUC: Calculate final amount:\n(price - discount) × quantity

PharmacistUC -> OrderRepo: update(orderId, {status: APPROVED,\nquantity, deliveryManNote, totalAmount})
activate OrderRepo
OrderRepo -> DB: updateOne({id: orderId},\n{status: "APPROVED", ...})
activate DB
DB --> OrderRepo: approved order
deactivate DB
OrderRepo --> PharmacistUC: order
deactivate OrderRepo

PharmacistUC --> Controller: approved order
deactivate PharmacistUC

Controller -> NotificationSvc: sendOrderApprovalNotification(order)
activate NotificationSvc
NotificationSvc --> Controller: notification sent
deactivate NotificationSvc

Controller --> WebApp: {success: true, order}
deactivate Controller
WebApp --> Pharmacist: Order confirmed and approved
deactivate WebApp

== Track Order Status ==

Patient -> MobileApp: Check order status
activate MobileApp
MobileApp -> Controller: GET /basket/history
activate Controller

Controller -> OrderRepo: findAllByPatientId(patientId)
activate OrderRepo
OrderRepo -> DB: find({patientId})\n.sort({createdAt: -1})
activate DB
DB --> OrderRepo: order history
deactivate DB
OrderRepo --> Controller: orders
deactivate OrderRepo

Controller --> MobileApp: {success: true, orders}
deactivate Controller
MobileApp --> Patient: Display order history with status
deactivate MobileApp

== Mark Order as Delivered ==

DeliveryPerson -> WebApp: Access delivery management interface
activate WebApp
WebApp -> Controller: GET /orders/filter?\nstatus=APPROVED&assignedTo=deliveryPersonId
activate Controller

Controller -> OrderRepo: findByFilters({status: APPROVED,\nassignedTo: deliveryPersonId})
activate OrderRepo
OrderRepo -> DB: find({status: "APPROVED",\nassignedTo: deliveryPersonId})
activate DB
DB --> OrderRepo: assigned orders
deactivate DB
OrderRepo --> Controller: orders list
deactivate OrderRepo

Controller --> WebApp: {success: true, orders}
deactivate Controller
WebApp --> DeliveryPerson: Display assigned orders for delivery
deactivate WebApp

DeliveryPerson -> WebApp: Select order and mark as delivered
activate WebApp
WebApp -> Controller: PUT /basket/markDelivered/{orderId}
activate Controller

Controller -> Controller: Check user role\n(delivery/admin)

Controller -> DeliveredUC: execute(orderId)
activate DeliveredUC

DeliveredUC -> OrderRepo: findById(orderId)
activate OrderRepo
OrderRepo -> DB: findOne({id: orderId})
activate DB
DB --> OrderRepo: order details
deactivate DB
OrderRepo --> DeliveredUC: order
deactivate OrderRepo

DeliveredUC -> OrderRepo: update(orderId, {status: DELIVERED,\ndeliveredAt: now()})
activate OrderRepo
OrderRepo -> DB: updateOne({id: orderId},\n{status: "DELIVERED", deliveredAt: now()})
activate DB
DB --> OrderRepo: delivered order
deactivate DB
OrderRepo --> DeliveredUC: order
deactivate OrderRepo

DeliveredUC --> Controller: delivered order
deactivate DeliveredUC

Controller -> NotificationSvc: sendDeliveryConfirmationNotification(order)
activate NotificationSvc
NotificationSvc --> Controller: notification sent
deactivate NotificationSvc

Controller --> WebApp: {success: true, order}
deactivate Controller
WebApp --> DeliveryPerson: Order marked as delivered successfully
deactivate WebApp

@enduml
`