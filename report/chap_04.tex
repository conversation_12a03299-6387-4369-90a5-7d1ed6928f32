\chapter{Sprint 2: Prescription Management Implementation}

\section{Introduction}
This chapter details the design and implementation of Sprint 2, focusing on the prescription management features for the MED4SOLUTIONS platform. Sprint 2 builds upon the authentication foundation established in Sprint 1, implementing comprehensive prescription upload, AI-powered OCR processing, pharmacy validation workflows, and no\-ti\-fi\-ca\-tion systems across mobile and web platforms.

\section{Sprint 2 Requirements Analysis}

\subsection{Prescription Upload and Management Use Case Diagram}
Figure \ref{fig:prescription_upload_usecase} illustrates the prescription upload and management module, covering prescription upload via photos or PDF files, AI-based OCR processing, and prescription management capabilities for patients and pharmacists.

\begin{landscape}
\begin{figure}[p]
    \centering
    \includegraphics[width=\paperwidth,height=\paperheight,keepaspectratio]{img/sprint2/prescription_upload_usecase.png}
    \caption{Prescription Upload and Management Use Case Diagram}
    \label{fig:prescription_upload_usecase}
\end{figure}
\end{landscape}


The prescription upload system allows patients to upload prescriptions through multiple methods including camera capture, photo gallery selection, and PDF file upload. The system processes these uploads using AI-powered OCR technology to extract medication information while maintaining the original files for pharmacy validation.

\subsection{Prescription Validation and Processing Use Case Diagram}
Figure \ref{fig:prescription_validation_usecase} demonstrates the prescription validation workflow where pharmacists review, approve, or reject uploaded prescriptions, ensuring medication safety and regulatory compliance.

\begin{landscape}
\begin{figure}[p]
    \centering
    \includegraphics[width=\paperwidth,height=\paperheight,keepaspectratio]{img/sprint2/lll.png}
    \caption{Prescription Validation and Processing Use Case Diagram}
    \label{fig:prescription_validation_usecase}
\end{figure}
\end{landscape}


The validation system enables pharmacists to review prescription details, verify medication information extracted by OCR, approve valid prescriptions, or reject prescriptions with appropriate feedback to patients. The system maintains audit trails and ensures compliance with pharmaceutical regulations.

\section{Sprint 2 Backlog}
The Sprint 2 backlog focuses on implementing prescription management features extracted from the main product backlog. This sprint covers user stories related to prescription upload, AI-powered OCR processing, pharmacy validation, and no\-ti\-fi\-ca\-tion systems for both mobile and web platforms.

\begin{center}
\begin{longtable}{|c|>{\raggedright\arraybackslash}p{3.2cm}|c|>{\raggedright\arraybackslash}p{3.5cm}|c|>{\raggedright\arraybackslash}p{3.5cm}|c|}
\hline
\textbf{ID\_F} & \textbf{Feature} & \textbf{ID\_U} & \textbf{User Story} & \textbf{ID\_T} & \textbf{Task} & \textbf{State} \\
\hline
\endfirsthead

\hline
\textbf{ID\_F} & \textbf{Feature} & \textbf{ID\_U} & \textbf{User Story} & \textbf{ID\_T} & \textbf{Task} & \textbf{State} \\
\hline
\endhead

\hline
\endfoot

\hline
\caption{Sprint 2 Backlog with Tasks}
\label{table:sprint2_backlog}
\endlastfoot

3 & Prescription Management & 3.1 & As a patient, I want to upload my prescription as photos or PDF files or take instant photos & 3.1.1 & Design prescription upload UI for mobile app & Done \\
\cline{5-7}
 &  &  &  & 3.1.2 & Implement camera integration for instant photo capture & Done \\
\cline{5-7}
 &  &  &  & 3.1.3 & Implement gallery selection for existing photos & Done \\
\cline{5-7}
 &  &  &  & 3.1.4 & Implement PDF file upload functionality & Done \\
\cline{3-7}
 &  & 3.2 & As a patient, I want the system to process my prescription using AI & 3.2.1 & Integrate OCR service for prescription processing & Done \\
\cline{5-7}
 &  &  &  & 3.2.2 & Implement medicine extraction algorithms & Done \\
\cline{5-7}
 &  &  &  & 3.2.3 & Create structured data format for extracted medicines & Done \\
\cline{5-7}
 &  &  &  & 3.2.4 & Implement error handling for OCR failures & Done \\
\cline{3-7}
 &  & 3.3 & As a patient, I want to receive a no\-ti\-fi\-ca\-tion when the pharmacy requires my original prescription & 3.3.1 & Design no\-ti\-fi\-ca\-tion system for prescription requirements & Done \\
\cline{5-7}
 &  &  &  & 3.3.2 & Implement push no\-ti\-fi\-ca\-tions for mobile app & Done \\
\cline{5-7}
 &  &  &  & 3.3.3 & Implement email no\-ti\-fi\-ca\-tions for web platform & Done \\
\cline{3-7}
 &  & 3.4 & As a system, I want to use OCR to scan and translate prescriptions into structured format while retaining original files & 3.4.1 & Implement file storage system for original prescriptions & Done \\
\cline{5-7}
 &  &  &  & 3.4.2 & Create OCR processing pipeline & Done \\
\cline{5-7}
 &  &  &  & 3.4.3 & Implement structured data extraction and storage & Done \\
\cline{5-7}
 &  &  &  & 3.4.4 & Create validation rules for extracted data & Done \\
\hline
\end{longtable}
\end{center}

\vspace{1cm}

\section{Sprint 2 Design and Architecture}

\subsection{Prescription Management Class Diagram}
Figure \ref{fig:sprint2_class} presents the class diagram for Sprint 2, illustrating the relationships between Prescription, Patient, Pharmacy entities and their associated services for prescription management, OCR processing, and no\-ti\-fi\-ca\-tion handling.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\textwidth]{img/sprint2/kkk.png}
    \caption{Sprint 2 Class Diagram — Prescription Management}
    \label{fig:sprint2_class}
\end{figure}



The class diagram shows the core entities: Prescription (containing prescription data and file paths), Patient (linked to prescriptions), Pharmacy (handling validation), and the service layer including PrescriptionService, OcrService, and No\-ti\-fi\-ca\-tionService that handle business logic and AI processing.

\subsection{Sequence Diagrams}

\subsubsection{Prescription Upload and OCR Processing Sequence}
Figure \ref{fig:prescription_upload_sequence} demonstrates the prescription upload flow including file upload, OCR processing, medicine extraction, and no\-ti\-fi\-ca\-tion to pharmacists.

\begin{figure}[H]
\centering
\includegraphics[width=1\textwidth]{img/sprint2/prescription_upload_sequence.png}
\caption{Prescription Upload and OCR Processing Sequence Diagram}
\label{fig:prescription_upload_sequence}
\end{figure}

\subsubsection{Prescription Validation Sequence}
Figure \ref{fig:prescription_validation_sequence} illustrates the prescription validation workflow where pharmacists review uploaded prescriptions, approve or reject them, and send no\-ti\-fi\-ca\-tions to patients.

\begin{figure}[H]
\centering
\includegraphics[width=1\textwidth]{img/sprint2/prescription_validation_sequence.png}
\caption{Prescription Validation Sequence Diagram}
\label{fig:prescription_validation_sequence}
\end{figure}

\subsubsection{Prescription No\-ti\-fi\-ca\-tion Sequence}
Figure \ref{fig:prescription_notification_sequence} shows the no\-ti\-fi\-ca\-tion flow when prescriptions require original document pickup or when validation status changes.

\begin{figure}[H]
\centering
\includegraphics[width=1\textwidth]{img/sprint2/prescription_notification_sequence.png}
\caption{Prescription No\-ti\-fi\-ca\-tion Sequence Diagram}
\label{fig:prescription_notification_sequence}
\end{figure}

\section{Sprint 2 Implementation}

\subsection{Backend Implementation}
The backend implementation for Sprint 2 utilized NestJS framework with TypeScript, implementing a comprehensive prescription management system with AI-powered OCR capabilities. Key implementation highlights include:

\begin{itemize}
    \item \textbf{Prescription Entity}: Designed and implemented the Prescription entity with support for multiple file uploads, structured medicine data, and comprehensive status tracking.
    \item \textbf{OCR Integration}: Integrated Python-based OCR service for prescription text extraction, medicine identification, and structured data conversion.
    \item \textbf{File Management}: Implemented secure file upload and storage system supporting multiple image formats and PDF files.
    \item \textbf{No\-ti\-fi\-ca\-tion System}: Developed comprehensive no\-ti\-fi\-ca\-tion system for prescription status updates, pharmacy alerts, and patient com\-mu\-ni\-ca\-tions.
    \item \textbf{Validation Workflows}: Created pharmacy validation workflows with approval/rejection mechanisms and audit trail maintenance.
\end{itemize}

\subsection{Mobile Application Implementation}
The mobile application implementation focused on user-friendly prescription upload and management features:

\begin{itemize}
    \item \textbf{Camera Integration}: Implemented native camera functionality for instant prescription photo capture with image optimization.
    \item \textbf{File Upload Interface}: Created intuitive file selection interface supporting gallery photos and document uploads.
    \item \textbf{Prescription Management}: Developed comprehensive prescription viewing, editing, and status tracking capabilities.
    \item \textbf{Real-time No\-ti\-fi\-ca\-tions}: Implemented push no\-ti\-fi\-ca\-tion system for prescription status updates and pharmacy com\-mu\-ni\-ca\-tions.
    \item \textbf{Offline Support}: Added offline capability for prescription drafts and automatic sync when connectivity is restored.
\end{itemize}

\subsection{Web Platform Implementation}
The web platform implementation provided pharmacists and administrators with powerful prescription management tools:

\begin{itemize}
    \item \textbf{Prescription Dashboard}: Created comprehensive dashboard for prescription review, validation, and management.
    \item \textbf{OCR Review Interface}: Implemented interface for reviewing and correcting AI-extracted prescription data.
    \item \textbf{Validation Workflows}: Developed streamlined approval/rejection workflows with detailed feedback mechanisms.
    \item \textbf{Reporting System}: Created reporting tools for prescription analytics, processing times, and validation metrics.
    \item \textbf{Integration APIs}: Developed RESTful APIs for seamless integration with pharmacy management systems.
\end{itemize}

\section{Testing and Quality Assurance}
Sprint 2 implementation included comprehensive testing strategies to ensure accuracy and reliability:

\begin{itemize}
    \item \textbf{Unit Testing}: Implemented unit tests for prescription services, OCR processing, and validation logic.
    \item \textbf{Integration Testing}: Created integration tests for file upload, OCR processing, and no\-ti\-fi\-ca\-tion systems.
    \item \textbf{OCR Accuracy Testing}: Conducted extensive testing of OCR accuracy with various prescription formats and handwriting styles.
    \item \textbf{Performance Testing}: Performed load testing for file upload and processing capabilities under high user volumes.
    \item \textbf{User Acceptance Testing}: Conducted UAT with pharmacists and patients to validate functionality and user experience.
\end{itemize}

\section{Conclusion}
Sprint 2 successfully established the core prescription management capabilities for the MED4SOLUTIONS platform. The implementation provides intelligent, AI-powered prescription processing with comprehensive validation workflows across mobile and web platforms. The robust OCR integration and no\-ti\-fi\-ca\-tion systems create an efficient prescription management ecosystem that enhances patient care while ensuring pharmaceutical compliance and safety. The foundation established in Sprint 2 enables seamless integration with order management and delivery systems in subsequent sprints.




